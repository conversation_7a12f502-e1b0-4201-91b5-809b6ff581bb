import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { generateDemoBenefitRankings } from '@/lib/demo-analytics-generator'

// GET /api/analytics/benefit-rankings - Get benefit ranking analytics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d' // 7d, 30d, 90d
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    if (accessInfo.level === 'none') {
      return NextResponse.json(
        { error: 'Authentication required to access analytics' },
        { status: 401 }
      )
    }

    // If user is on demo mode, return demo data
    if (accessInfo.isDemoMode) {
      const demoData = generateDemoBenefitRankings(period, limit, category)
      return NextResponse.json(demoData)
    }

    // Ensure user_benefit_rankings table exists
    try {
      await query(`
        CREATE TABLE IF NOT EXISTS user_benefit_rankings (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
          ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, benefit_id)
        );
      `)

      // Create indexes if they don't exist
      await query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);')
      await query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);')
      await query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);')
      await query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);')
    } catch (tableError) {
      console.error('Error creating user_benefit_rankings table:', tableError)
      // Continue anyway - table might already exist
    }

    // Calculate date range for period
    const now = new Date()
    let startDate: Date
    
    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default: // 30d
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Build category filter
    let categoryFilter = ''
    const params: any[] = [startDate.toISOString()]
    let paramIndex = 2

    if (category) {
      categoryFilter = `AND b.category = $${paramIndex}`
      params.push(category)
      paramIndex++
    }

    // Get benefit ranking statistics
    const benefitStatsResult = await query(`
      SELECT
        b.id as benefit_id,
        b.name as benefit_name,
        b.category,
        b.icon,
        bc.display_name as category_display_name,
        COALESCE(COUNT(ubr.id), 0) as total_rankings,
        COALESCE(AVG(ubr.ranking::numeric), 0) as average_ranking,
        MIN(ubr.ranking) as best_ranking,
        MAX(ubr.ranking) as worst_ranking,
        COUNT(CASE WHEN ubr.ranking <= 3 THEN 1 END) as top_3_count,
        COUNT(CASE WHEN ubr.ranking >= 8 THEN 1 END) as bottom_3_count,
        STDDEV(ubr.ranking::numeric) as ranking_stddev
      FROM benefits b
      LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      LEFT JOIN user_benefit_rankings ubr ON b.id = ubr.benefit_id
        AND ubr.updated_at >= $1
      ${categoryFilter}
      GROUP BY b.id, b.name, b.category, b.icon, bc.display_name
      HAVING COUNT(ubr.id) > 0
      ORDER BY average_ranking ASC NULLS LAST, total_rankings DESC
      LIMIT $${paramIndex}
    `, [...params, limit])

    // Get ranking trends over time
    const trendResult = await query(`
      SELECT 
        DATE(ubr.updated_at) as date,
        b.name as benefit_name,
        AVG(ubr.ranking::numeric) as average_ranking,
        COUNT(ubr.id) as ranking_count
      FROM user_benefit_rankings ubr
      JOIN benefits b ON ubr.benefit_id = b.id
      WHERE ubr.updated_at >= $1
      ${categoryFilter.replace('b.category', 'b.category')}
      GROUP BY DATE(ubr.updated_at), b.name
      ORDER BY date DESC, average_ranking ASC
    `, params.slice(0, -1))

    // Get category breakdown
    const categoryBreakdownResult = await query(`
      SELECT 
        b.category,
        bc.display_name as category_display_name,
        COUNT(ubr.id) as total_rankings,
        AVG(ubr.ranking::numeric) as average_ranking,
        COUNT(CASE WHEN ubr.ranking <= 3 THEN 1 END) as top_3_count
      FROM benefits b
      LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      LEFT JOIN user_benefit_rankings ubr ON b.id = ubr.benefit_id 
        AND ubr.updated_at >= $1
      GROUP BY b.category, bc.display_name
      HAVING COUNT(ubr.id) > 0
      ORDER BY average_ranking ASC
    `, [startDate.toISOString()])

    // Get most improved/declined benefits (comparing to previous period)
    const previousStartDate = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()))
    
    const improvementResult = await query(`
      WITH current_period AS (
        SELECT 
          benefit_id,
          AVG(ranking::numeric) as avg_ranking
        FROM user_benefit_rankings
        WHERE updated_at >= $1 AND updated_at < $2
        GROUP BY benefit_id
      ),
      previous_period AS (
        SELECT 
          benefit_id,
          AVG(ranking::numeric) as avg_ranking
        FROM user_benefit_rankings
        WHERE updated_at >= $3 AND updated_at < $1
        GROUP BY benefit_id
      )
      SELECT 
        b.name as benefit_name,
        b.category,
        b.icon,
        cp.avg_ranking as current_avg,
        pp.avg_ranking as previous_avg,
        (pp.avg_ranking - cp.avg_ranking) as improvement
      FROM current_period cp
      JOIN previous_period pp ON cp.benefit_id = pp.benefit_id
      JOIN benefits b ON cp.benefit_id = b.id
      ORDER BY improvement DESC
      LIMIT 10
    `, [startDate.toISOString(), now.toISOString(), previousStartDate.toISOString()])

    // Calculate summary statistics
    const totalRankings = benefitStatsResult.rows.reduce((sum, row) => sum + parseInt(row.total_rankings), 0)
    const avgOverallRanking = benefitStatsResult.rows.length > 0 
      ? benefitStatsResult.rows.reduce((sum, row) => sum + parseFloat(row.average_ranking || '0'), 0) / benefitStatsResult.rows.length
      : 0

    return NextResponse.json({
      period,
      category: category || 'all',
      summary: {
        totalRankings,
        totalBenefitsRanked: benefitStatsResult.rows.length,
        averageOverallRanking: Math.round(avgOverallRanking * 100) / 100,
        mostImportantBenefit: benefitStatsResult.rows[0]?.benefit_name || null,
        leastImportantBenefit: benefitStatsResult.rows[benefitStatsResult.rows.length - 1]?.benefit_name || null
      },
      benefitStats: benefitStatsResult.rows.map(row => ({
        ...row,
        average_ranking: Math.round(parseFloat(row.average_ranking || '0') * 100) / 100,
        ranking_stddev: row.ranking_stddev ? Math.round(parseFloat(row.ranking_stddev) * 100) / 100 : null,
        importance_score: row.total_rankings > 0 ? Math.round((11 - parseFloat(row.average_ranking || '0')) * 10) : 0
      })),
      trends: trendResult.rows,
      categoryBreakdown: categoryBreakdownResult.rows.map(row => ({
        ...row,
        average_ranking: Math.round(parseFloat(row.average_ranking || '0') * 100) / 100
      })),
      improvements: improvementResult.rows.map(row => ({
        ...row,
        current_avg: Math.round(parseFloat(row.current_avg || '0') * 100) / 100,
        previous_avg: Math.round(parseFloat(row.previous_avg || '0') * 100) / 100,
        improvement: Math.round(parseFloat(row.improvement || '0') * 100) / 100
      })),
      generated_at: new Date().toISOString(),
      is_demo_data: false
    })

  } catch (error) {
    console.error('Error fetching benefit ranking analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefit ranking analytics' },
      { status: 500 }
    )
  }
}
